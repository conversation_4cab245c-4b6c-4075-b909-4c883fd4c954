"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
} from "@/components/ui/navigation-menu";
import { Badge } from "@/components/ui/badge";
import { Zap, Rocket, Bot, Cpu, Database, Mail, Phone, MapPin, XCircle, CheckCircle2, Code, MessageCircle, Cog, LifeBuoy } from "lucide-react";
import { GradientTracing } from "@/components/ui/gradient-tracing";
import Link from "next/link";
import { CpuBlock } from "@/components/ui/CpuBlock";
import Image from "next/image"; // Import Image
import { StarBorder } from "@/components/ui/star-border"; // Import StarBorder
import { useTranslations } from 'next-intl';

export default function Home() {
  const t = useTranslations('HomePage');

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-950 via-purple-900 to-purple-950">
      {/* Hero Section */}
      <div className="pt-32 pb-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="grid lg:grid-cols-2 gap-16 items-center">
            <div className="space-y-8">
              <h1 className="text-5xl lg:text-6xl font-bold text-white leading-tight">
                {t('heroTitle')}{" "}
                <span className="bg-gradient-to-r from-pink-500 to-purple-500 text-transparent bg-clip-text">
                  {t('heroHighlight')}
                </span>
              </h1>
              <p className="text-xl text-purple-100/80">
                {t('heroSubtitle')}
              </p>
              <div className="space-y-4">
                <Link href="/contact">
                  <Button
                    size="default"
                    className="bg-gradient-to-r from-pink-600 to-purple-600 hover:from-pink-500 hover:to-purple-500 text-white shadow-lg shadow-purple-500/20 w-full sm:w-auto transition-all duration-200 hover:scale-105 text-sm py-2 px-4"
                  >
                    {t('launchWithUs')}
                  </Button>
                </Link>
                <div className="flex items-center gap-4 text-purple-100/60 text-sm">
                  <div className="flex items-center gap-2">
                    <Zap className="w-4 h-4" />
                    <span>{t('freeConsultation')}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Rocket className="w-4 h-4" />
                    <span>{t('fastTurnaround')}</span>
                  </div>
                </div>
              </div>
            </div>
            <div className="relative z-10 transform lg:scale-110 lg:translate-x-5">
              <CpuBlock />
            </div>
          </div>
        </div>
      </div>

      {/* Services Section (Previously Benefits) */}
      <div className="py-20 px-4 sm:px-6 lg:px-8 bg-white">
        <div className="max-w-7xl mx-auto">
           <h2 className="text-5xl pb-1 font-bold text-center mb-16 bg-gradient-to-r from-pink-600 to-purple-600 text-transparent bg-clip-text">
            {t('coreServicesTitle')}
          </h2>
          <div className="flex flex-wrap justify-center gap-8">
            {[
              {
                icon: Code,
                title: t('fullStackTitle'),
                description: t('fullStackDesc'),
              },
              {
                icon: MessageCircle,
                title: t('chatbotsTitle'),
                description: t('chatbotsDesc'),
              },
              // {
              //   icon: Cog,
              //   title: "Automations & Backend Logic",
              //   description: "Streamline tasks, trigger actions, and manage data behind the scenes with custom backend workflows.",
              // },
               {
                icon: LifeBuoy,
                title: t('supportTitle'),
                description: t('supportDesc'),
              },
              {
                icon: Rocket, // Kept from original
                title: t('deploymentTitle'),
                description: t('deploymentDesc'),
              },
            ].map((service, index) => (
              <div
                key={index}
                className="bg-gray-50 rounded-xl p-8 border border-gray-200 hover:shadow-lg transition-all duration-300 ease-in-out hover:border-purple-200 w-full md:w-[calc(50%-1rem)] lg:w-[calc(50%-1rem)]"
              >
                <div className="bg-gradient-to-br from-pink-500 to-purple-500 w-12 h-12 rounded-lg flex items-center justify-center mb-6">
                  <service.icon className="w-6 h-6 text-white" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">{service.title}</h3>
                <p className="text-gray-600">{service.description}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
      {/* Tech Stack Section */}
      <div className="py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-purple-950 via-purple-900 to-purple-950">
        <div className="max-w-7xl mx-auto">
          <div>
            <h2 className="text-4xl font-bold text-center mb-8 text-white">
              {t('techStackTitle')} <span className="bg-gradient-to-r from-pink-500 to-purple-500 text-transparent bg-clip-text">{t('techStackHighlight')}</span>
            </h2>
            <p className="text-center text-purple-100/70 mb-10 max-w-3xl mx-auto">
              {t('techStackDesc')}
            </p>
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-8 max-w-5xl mx-auto">
              {[
                { name: "JavaScript", logo: "/logo/tech-stack/javascript.svg" },
                { name: "Node.js", logo: "/logo/tech-stack/nodejs.svg" },
                { name: "TypeScript", logo: "/logo/tech-stack/typescript.svg" },
                { name: "Vue.js", logo: "/logo/tech-stack/vuejs.svg" },
                { name: "Python", logo: "/logo/tech-stack/python.svg" },
                { name: "Astro", logo: "/logo/tech-stack/astro.svg" },
                { name: "React / React Native", logo: "/logo/tech-stack/react.svg" },
                { name: "Next.js", logo: "/logo/tech-stack/nextjs.svg" },
                { name: "Angular", logo: "/logo/tech-stack/angular.svg" },
                { name: "Supabase", logo: "/logo/tech-stack/supabase.svg" },
                { name: "Svelte", logo: "/logo/tech-stack/svelte.svg" },
                { name: "Gatsby", logo: "/logo/tech-stack/gatsby.svg" }
              ].map((tech, index) => (
                <div
                  key={index}
                  className="flex flex-col items-center justify-center p-4 bg-purple-900/20 backdrop-blur-sm rounded-lg border border-purple-700/20 hover:bg-purple-900/30 transition-all duration-200 hover:scale-105 transform"
                >
                  <div className="w-12 h-12 mb-3 flex items-center justify-center">
                    <img
                      src={tech.logo}
                      alt={`${tech.name} logo`}
                      className="max-w-full max-h-full"
                    />
                  </div>
                  <span className="text-purple-100 text-sm font-medium text-center">{tech.name}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Comparison Section */}
      <div className="pt-26 pb-20 px-4 sm:px-6 lg:px-8 bg-gray-50"> {/* Increased top padding */}
        <div className="max-w-7xl mx-auto">
          {/* Apply gradient to title with additional top margin */}
          <h2 className="text-4xl lg:text-5xl font-bold text-center mb-16 bg-gradient-to-r from-pink-600 to-purple-600 text-transparent bg-clip-text pt-8 pb-1 leading-relaxed">
            {t('whyChooseTitle')}
          </h2>
          <div className="grid md:grid-cols-2 gap-8 lg:gap-12"> {/* Adjusted gap */}
            {/* Other Specialists Column - Subtle red tint, increased padding, hover effect */}
            <div className="bg-red-50/30 rounded-xl p-10 border border-red-200 shadow-lg hover:scale-[1.02] transition-transform duration-300">
              <h3 className="text-2xl font-semibold text-gray-800 mb-6">{t('otherSpecialists')}</h3> {/* Adjusted text color slightly */}
              <ul className="space-y-5"> {/* Increased spacing */}
                <li className="flex items-start gap-4"> {/* Increased gap */}
                  <XCircle className="w-7 h-7 text-red-500 flex-shrink-0 mt-0.5" /> {/* Slightly larger icon */}
                  <span className="text-gray-600"> {/* Changed text color */}
                    <strong className="text-gray-800 block">{t('limitedResultsTitle')}</strong>
                    {t('limitedResultsDesc')}
                  </span>
                </li>
                <li className="flex items-start gap-4"> {/* Increased gap */}
                  <XCircle className="w-7 h-7 text-red-500 flex-shrink-0 mt-0.5" /> {/* Slightly larger icon */}
                  <span className="text-gray-600">
                    <strong className="text-gray-800 block">{t('confusingProcessTitle')}</strong>
                    {t('confusingProcessDesc')}
                  </span>
                </li>
                <li className="flex items-start gap-4"> {/* Increased gap */}
                  <XCircle className="w-7 h-7 text-red-500 flex-shrink-0 mt-0.5" /> {/* Slightly larger icon */}
                  <span className="text-gray-600">
                    <strong className="text-gray-800 block">{t('oneWayCommTitle')}</strong>
                    {t('oneWayCommDesc')}
                  </span>
                </li>
              </ul>
            </div>

            {/* UpZera Column - Subtle green tint, increased padding, hover effect */}
            <div className="bg-green-50/30 rounded-xl p-10 border border-green-200 shadow-lg hover:scale-[1.02] transition-transform duration-300">
              <h3 className="text-2xl font-semibold text-gray-800 mb-6">{t('weAtUpZera')}</h3> {/* Adjusted text color slightly */}
              <ul className="space-y-5"> {/* Increased spacing */}
                <li className="flex items-start gap-4"> {/* Increased gap */}
                  <CheckCircle2 className="w-7 h-7 text-green-500 flex-shrink-0 mt-0.5" /> {/* Slightly larger icon */}
                  <span className="text-gray-600">
                    <strong className="text-gray-800 block">{t('affordableTitle')}</strong>
                    {t('affordableDesc')}
                  </span>
                </li>
                <li className="flex items-start gap-4"> {/* Increased gap */}
                  <CheckCircle2 className="w-7 h-7 text-green-500 flex-shrink-0 mt-0.5" /> {/* Slightly larger icon */}
                  <span className="text-gray-600">
                    <strong className="text-gray-800 block">{t('clearProcessTitle')}</strong>
                    {t('clearProcessDesc')}
                  </span>
                </li>
                <li className="flex items-start gap-4"> {/* Increased gap */}
                  <CheckCircle2 className="w-7 h-7 text-green-500 flex-shrink-0 mt-0.5" /> {/* Slightly larger icon */}
                  <span className="text-gray-600">
                    <strong className="text-gray-800 block">{t('personalTitle')}</strong>
                    {t('personalDesc')}
                  </span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-5xl mx-auto bg-gradient-to-r from-pink-600 via-fuchsia-600 to-purple-600 rounded-2xl p-12 shadow-xl overflow-hidden relative">
          {/* Add subtle background blur elements to match the image background */}
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(255,255,255,0.1),rgba(255,255,255,0))] opacity-70"></div>
          {/* Add additional gradient overlay to better match the image */}
          <div className="absolute inset-0 bg-gradient-to-br from-pink-600/20 via-fuchsia-600/10 to-purple-600/30"></div>
          <div className="grid md:grid-cols-2 gap-12 items-center">
            {/* Image Column */}
            <div className="flex justify-center relative z-10">
              <div className="relative">
                {/* Add a subtle glow effect behind the image */}
                <div className="absolute inset-0 bg-purple-500/20 blur-2xl rounded-full transform scale-90"></div>
                <Image
                  src="/home/<USER>"
                  alt="Chat bubble illustration"
                  width={350} // Adjusted size
                  height={280} // Adjusted size
                  className="object-contain relative z-10"
                  style={{
                    filter: 'drop-shadow(0 8px 26px rgba(91, 33, 182, 0.3))',
                    maskImage: 'radial-gradient(circle, white 5%, transparent 75%)',
                    WebkitMaskImage: 'radial-gradient(circle, white 70, transparent 100%)', // For Safari compatibility
                    height: "auto" // Maintain aspect ratio
                  }}
                />
              </div>
            </div>
            {/* Text & Button Column */}
            <div className="relative z-10">
              {/* Changed text color */}
              <h2 className="text-4xl lg:text-5xl font-bold text-white leading-tight">
              Got an idea? Let’s explore it together.
              </h2>
              <p className="mt-4 text-lg text-white"> {/* Adjusted text color for contrast */}
                {t('ctaDesc')}
              </p>
              <div className="mt-8">
                <Link href="/contact">
                  {/* Replace Button with StarBorder */}
                  <StarBorder className="w-full sm:w-auto"> {/* Added responsive width */}
                    {/* Apply gradient and text color to the inner div */}
                    <div className="relative z-1 border-none text-white text-center text-sm py-2 px-6 rounded-[20px] bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-500 hover:to-purple-500 transition-colors font-medium shadow-lg shadow-purple-500/30">
                      {t('useFreeConsultation')}
                    </div>
                  </StarBorder>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
