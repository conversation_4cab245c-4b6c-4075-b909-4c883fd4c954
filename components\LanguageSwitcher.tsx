"use client";

import { useLocale } from 'next-intl';
import { useRouter, usePathname } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Globe } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

const languages = [
  { code: 'en', name: 'English', flag: '🇪🇳' },
  { code: 'lt', name: 'Lietuvių', flag: '🇱🇹' },
];

interface LanguageSwitcherProps {
  keepMenuOpen?: boolean;
}

export default function LanguageSwitcher({ keepMenuOpen = false }: LanguageSwitcherProps) {
  const locale = useLocale();
  const router = useRouter();
  const pathname = usePathname();

  const handleLanguageChange = (newLocale: string) => {
    // Remove the current locale from the pathname
    const pathWithoutLocale = pathname?.replace(`/${locale}`, '') || '';

    // If we want to keep the menu open, preserve the menu state
    if (keepMenuOpen) {
      // Ensure the menu state is preserved during navigation
      sessionStorage.setItem('mobileMenuOpen', 'true');
      // Use replace instead of push to avoid navigation history issues
      router.replace(`/${newLocale}${pathWithoutLocale}`);
    } else {
      router.push(`/${newLocale}${pathWithoutLocale}`);
    }
  };

  const currentLanguage = languages.find(lang => lang.code === locale);

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          className="text-white/80 hover:text-white hover:bg-purple-800/50 transition-colors bg-transparent border-none"
        >
          <Globe className="h-4 w-4 mr-2" />
          <span className="hidden sm:inline">{currentLanguage?.name}</span>
          <span className="sm:hidden">{currentLanguage?.flag}</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align="end"
        className="bg-purple-900 border-purple-700 min-w-[140px] w-[140px] overflow-hidden"
        sideOffset={8}
        alignOffset={0}
        avoidCollisions={true}
        collisionPadding={16}
        side="bottom"
      >
        {languages.map((language) => (
          <DropdownMenuItem
            key={language.code}
            onClick={() => handleLanguageChange(language.code)}
            className={`cursor-pointer text-white hover:bg-purple-800 focus:bg-purple-800 transition-colors py-3 px-4 text-base ${
              locale === language.code ? 'bg-purple-800' : ''
            }`}
          >
            <span className="mr-3 text-lg">{language.flag}</span>
            <span className="font-medium">{language.name}</span>
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
